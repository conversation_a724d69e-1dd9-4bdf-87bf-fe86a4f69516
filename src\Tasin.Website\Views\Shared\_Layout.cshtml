﻿<!DOCTYPE html>
<html lang="en">

<head>
    @using Tasin.Website.DAL.Services
    @inject IApplicationConfiguration AppSettings
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/x-icon" href="@AppSettings.WebsiteInfo.SiteUILogoUrl">

    <title>@ViewData["Title"]</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" asp-append-version="true" />

    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/Tasin.Website.styles.css" asp-append-version="true" />
    <link href="~/lib/fontawesome-free-6.5.2-web/css/all.min.css" rel="stylesheet" />
    <script src="~/js/enum.js" asp-append-version="true"></script>
    @* jquery *@
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    @* kendo *@
    <script src="~/lib/kendo/js/jquery.min.js"></script>
    <script src="~/lib/kendo/js/jszip.min.js"></script>
    <script src="~/lib/kendo/js/kendo.all.min.js"></script>
    <script src="~/js/moment.min.js"></script>
    @*  <link href="~/lib/kendo/styles/kendo.common.min.css" rel="stylesheet">
    <link href="~/lib/kendo/styles/kendo.default.mobile.min.css" rel="stylesheet"> *@

    <link href="~/lib/kendo/styles/kendo.common-bootstrap.min.css" rel="stylesheet" />
    <link href="~/lib/kendo/styles/kendo.bootstrap.min.css" rel="stylesheet" />
    <link href="~/lib/kendo/styles/kendo.bootstrap.mobile.min.css" rel="stylesheet" />

    <script src="~/lib/kendo/js/messages/kendo.messages.vi-VN.min.js"></script>
    <script src="~/lib/kendo/js/cultures/kendo.culture.vi-VN.min.js"></script>
    <script src="~/js/login/changepassword.js" asp-append-version="true"></script>
    <script src="~/js/permission/index.js" asp-append-version="true"></script>
    <script>
        kendo.culture("vi-VN");
    </script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    <script>
        const isMobile = {
            Android: function () {
                return navigator.userAgent.match(/Android/i);
            },
            BlackBerry: function () {
                return navigator.userAgent.match(/BlackBerry/i);
            },
            iOS: function () {
                return navigator.userAgent.match(/iPhone|iPad|iPod/i);
            },
            Opera: function () {
                return navigator.userAgent.match(/Opera Mini/i);
            },
            Windows: function () {
                return navigator.userAgent.match(/IEMobile/i) || navigator.userAgent.match(/WPDesktop/i);
            },
            any: function () {
                return (isMobile.Android() || isMobile.BlackBerry() || isMobile.iOS() || isMobile.Opera() || isMobile.Windows());
            }
        };

        var _isMobile = (isMobile.any() != null ? true : false);
        var appSettingJs = @Html.Raw(Json.Serialize(@AppSettings));
        var Userdata;
    </script>

    <style>
        ul {
            /* list-style-type: none; */
            text-align: justify;
        }

        .k-grid table {
            width: 100% !important;
        }

        /* Notifications */
        .k-notification h3 {
            padding: 30px 10px 5px;
            font-size: 1em;
            line-height: normal;
        }

        .k-notification img {
            margin: 20px;
            float: left;
        }

        .k-notification {
            margin-bottom: 8px;
        }

        /* Info template */
        .wrong-temp {
            background-color: #c1570b;
            color: black;
            min-width: 300px;
            max-width: 400px;
            min-height: 100px;
        }

        /* Error template */
        .wrong-pass {
            background-color: #ba4e4e;
            color: black;
            min-width: 300px;
            max-width: 400px;
            min-height: 100px;
        }

        /* Success template */
        .upload-success {
            background-color: #3bb23b;
            min-width: 300px;
            max-width: 400px;
            min-height: 100px;
        }

        .customClass {
            z-index: 999999 !important;
        }

        span#badge-sent:hover {
            color: white;
        }
    </style>
</head>

<body class=" nav-fixed" style="background-color: rgb(0 0 0 / 20%);">
    @* <header> *@
    <nav class="topnav navbar navbar-expand shadow justify-content-between justify-content-sm-start navbar-light bg-white ps-0 pe-0 py-0"
        style="box-shadow:none;background: transparent linear-gradient(90deg, #0E8C95 0%, #03CCDB 100%) 0% 0% no-repeat padding-box;"
        id="sidenavAccordion">
        <button class="btn btn-icon btn-transparent-dark order-0 order-lg-0 me-0 ms-0" id="sidebarToggle">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewbox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="feather feather-menu">
                <line x1="3" y1="12" x2="21" y2="12"></line>
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
        </button>

        <div class="navbar-brand ps-0 ms-sm-0 text-decoration-none" style="flex: 1;">
            <a class="d-flex align-items-center text-decoration-none" style="width: 184px;" href="/">
                <img src="@AppSettings.WebsiteInfo.SiteUILogoUrl" style="height: 44px;">
                <span id="titleHeader" class="text-primary text-decoration-none"
                    style="font-size: 18px;color:white !important; margin: 0; padding-left: 10px;">@AppSettings.WebsiteInfo.NameWebsite</span>
            </a>
        </div>


        <!-- Navbar Items-->
        <ul class="navbar-nav align-items-center ms-auto">
            <!-- User Dropdown-->
            <li class="nav-item dropdown no-caret dropdown-user ">
                <a class="btn btn-icon btn-transparent-dark dropdown-toggle" id="navbarDropdownUserImage"
                    href="javascript:void(0);" role="button" data-bs-toggle="dropdown" aria-haspopup="true"
                    aria-expanded="false">
                    <i class="fa-solid fa-user fa-1_5x"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-end border-0 shadow animated--fade-in-up"
                    aria-labelledby="navbarDropdownUserImage">
                    <h6 class="dropdown-header d-flex align-items-center justify-content-between" style="">
                        <div class="d-flex w-75  align-items-center ">
                            <i class="fa-solid fa-user fa-1_5x me-2"></i>
                            <div class="dropdown-user-details ">
                                <div class="dropdown-user-details-name" title="Username" id="pnAccount">Chào Username !
                                </div>
                                @* <div class="dropdown-user-details-email"><EMAIL></div> *@
                            </div>

                        </div>
                        @* <div id="div_user_QRcode" class="ms-2" style="cursor: pointer;">
                            <i class="fa fa-qrcode fa-2x text-primary"></i>
                        </div> *@
                    </h6>
                    <div class="dropdown-divider"></div>
                    <button class="dropdown-item change-password" runat="server" href="#">
                        <div class="dropdown-item-icon">
                            <i class="fa-solid fa-gear"></i>
                        </div>
                        Đổi mật khẩu
                    </button>
                    <div id="ChangePassword" style="display:none;">
                        <form id="FormChangePassword"></form>
                    </div>
                    <a class="dropdown-item logout_btn" href="/Account/Logout" style="color:black;">
                        <div class="dropdown-item-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewbox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="feather feather-log-out">
                                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                                <polyline points="16 17 21 12 16 7"></polyline>
                                <line x1="21" y1="12" x2="9" y2="12"></line>
                            </svg>
                        </div>
                        Đăng xuất
                    </a>
                </div>
            </li>
        </ul>
    </nav>

    @* </header> *@

    <div id="layoutSidenav">
        <div id="layoutSidenav_nav">
            <nav class="sidenav shadow-right sidenav-light"
                style="background: transparent linear-gradient(180deg, #148F96 0%, #1C4485 100%) 0% 0% no-repeat padding-box;">
                <div class="sidenav-menu" style="border-right: 1px solid var(--bs-color-global-gray40);">
                    <div class="nav accordion px-2" id="accordionSidenav">
                    </div>
                </div>
            </nav>
        </div>
        <div id="layoutSidenav_content">
            <main style="width: 100%;height: 100%;" role="main" class="pb-3">
                <div class="container-fluid  px-4 mt-4">
                    @RenderBody()

                </div>
                <span id="notification" style="display:none;"></span>
            </main>
            <footer class="border-top footer text-center text-muted">
                <div class="container">
                    &copy; @AppSettings.WebsiteInfo.SiteUITitleFooter
                </div>
            </footer>
        </div>
    </div>
    <div id="loadingIndicator"
        style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255, 255, 255, 0.8);opacity:0.5; z-index: 99999;">
        <div class="k-loading-image" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
        </div>
        <div class="k-loading-color"></div>
    </div>
    <!-- Modal -->
    <div class="modal fade " id="QRcode_Popup" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
        aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="staticBackdropLabel">Quét QR Code Telegram để nhận thông báo!</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img alt="QR Code" class="imgQR" src="" />
                </div>
                <div class="modal-footer" style="color:red;font-size:15px;">
                    Lưu ý: cần dùng tài khoản telegram theo số điện thoại đã đăng ký!
                </div>
            </div>
        </div>
    </div>

    <script id="wrongTemplate" type="text/x-kendo-template">
        <div class="wrong-temp">
            <div class="d-flex flex-column">
                <div>
                    <img src="../content/images/notification/error-icon.png" />
                    <h3>#= title #</h3>
                </div>
                <div class="px-2">
                    <p style="word-wrap: break-word;white-space: -moz-pre-wrap;white-space: pre-wrap;text-indent: 10px;">#= message #</p>
                </div>
            </div>
        </div>
    </script>

    <script id="errorTemplate" type="text/x-kendo-template">
        <div class="wrong-pass">
            <div class="d-flex flex-column">
                <div>
                    <img src="../content/images/notification/error-icon.png" />
                    <h3>#= title #</h3>
                </div>
                <div class="px-2">
                    <p style="word-wrap: break-word;white-space: -moz-pre-wrap;white-space: pre-wrap;text-indent: 10px;">#= message #</p>
                </div>
            </div>
        </div>
    </script>

    <script id="successTemplate" type="text/x-kendo-template">
        <div class="upload-success">
            <div class="d-flex flex-column">
                <div>
                    <img src="../content/images/notification/success-icon.png" />
                    <h3>#= title #</h3>
                </div>
                <div class="px-2">
                    <p style="word-wrap: break-word;white-space: -moz-pre-wrap;white-space: pre-wrap;text-indent: 10px;">#= message #</p>
                </div>
            </div>
        </div>
    </script>

    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/css/scriptss.js" asp-append-version="true"></script>

    <script src="~/js/kendogridresize/notification.js" asp-append-version="true"></script>

    <script src="~/js/kendogridresize/resize.js" asp-append-version="true"></script>
    <script src="~/js/notificationforuser/index.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)

    <script>
        function menu(EnumRoleList) {

            if (!EnumRoleList) return;
            let listDanhMuc = [1, 5, 9, 13, 17, 21, 25, 29, 33, 37];
            let html = "";
            if (EnumRoleList.find(e => listDanhMuc.includes(e))) {
                html += ` <a id="category_" class="nav-link my-1 collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#menu-category" aria-expanded="false" aria-controls="menu-category">
                                                <div class="nav-link-icon"><i class="fa-solid fa-bars-staggered fa-1_5x me-2"></i></div>
                                                Danh mục
                                                <div class="sidenav-collapse-arrow">
                                                    <i class="fas fa-angle-down"></i>
                                                </div>
                                            </a>`;
                html += `
                            <div class="nav collapse" id="menu-category" data-bs-parent="#accordionSidenav">
                                 <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavPages">`;
                if (EnumRoleList.find(e => e == 1)) {
                    html += `<a class="nav-link _permission_" data-enum="1" href="/User/Index">Người dùng</a>`;
                }
                if (EnumRoleList.find(e => e == 5)) {
                    html += `<a class="nav-link _permission_" data-enum="5" href="/Vendor/Index">Nhà cung cấp</a>`;
                }
                if (EnumRoleList.find(e => e == 9)) {
                    html += `<a class="nav-link _permission_" data-enum="9" href="/Customer/Index">Khách hàng</a>`;
                }
                if (EnumRoleList.find(e => e == 13)) {
                    html += `<a class="nav-link _permission_" data-enum="13" href="/Product/Index">Hàng hoá</a>`;
                }
                if (EnumRoleList.find(e => e == 17)) {
                    html += `<a class="nav-link _permission_" data-enum="17" href="/Unit/Index">Đơn vị</a>`;
                }
                if (EnumRoleList.find(e => e == 21)) {
                    html += `<a class="nav-link _permission_" data-enum="21" href="/Category/Index">Quy cách</a>`;
                }
                //ko dùng nữa
                // if (EnumRoleList.find(e => e == 25)){
                //     html += `<a class="nav-link _permission_" data-enum="25" href="/">Cấu hình thuế</a>`;
                // }
                if (EnumRoleList.find(e => e == 29)) {
                    html += `<a class="nav-link _permission_" data-enum="29" href="/SpecialProductTaxRate/Index">Loại mặt hàng</a>`;
                }
                if (EnumRoleList.find(e => e == 33)) {
                    html += `<a class="nav-link _permission_" data-enum="33" href="/Material/Index">Nguyên liệu</a>`;
                }
                if (EnumRoleList.find(e => e == 37)) {
                    html += `<a class="nav-link _permission_" data-enum="37" href="/ProcessingType/Index">Phân loại hàng hoá</a>`;
                }
                html += `</nav>
                                            </div>`;
            }

            if (EnumRoleList.find(e => e == 40)) {
                html += `<a class="nav-link my-1 _permission_" data-enum="40" href="/PurchaseOrder/Index">
                                <i class="fa-solid fa-plus fa-1_5x me-2"></i>
                                Tạo đơn hàng
                            </a>`;
            }
            if (EnumRoleList.find(e => e == 41)) {
                html += `<a class="nav-link my-1 _permission_" data-enum="41" href="/PurchaseOrder/Index">
                                <i class="fa-solid fa-bars-progress fa-1_5x me-2"></i>
                                Danh sách đơn hàng
                            </a>`;
            }
            if (EnumRoleList.find(e => e == 44)) {
                html += `<a class="nav-link  my-1 _permission_" data-enum="44" href="/PurchaseAgreement/Index">
                                <i class="fa-solid fa-plus fa-1_5x me-2"></i>
                                Tổng hợp đơn
                                        <span id="badge-sent" data-role="badge" class="k-badge k-badge-solid k-badge-solid-success k-badge-md k-rounded-full k-badge-inline k-ml-2" style="background-color: var(--bs-danger);">34</span>
                            </a>`;
            }
            if (EnumRoleList.find(e => e == 45)) {
                html += `<a class="nav-link  my-1 _permission_" data-enum="45" href="/PurchaseAgreement/Index">
                                <i class="fa-solid fa-diagram-project fa-1_5x me-2"></i>
                                Danh sách tổng hợp đơn
                            </a>`;
            }

            // Báo cáo menu
            let listBaoCao = [41, 45]; // READ_PURCHASE_AGREEMENT, READ_PURCHASE_ORDER permissions
            if (EnumRoleList.find(e => listBaoCao.includes(e))) {
                html += ` <a id="report_" class="nav-link my-1 collapsed" href="javascript:void(0);" data-bs-toggle="collapse" data-bs-target="#menu-report" aria-expanded="false" aria-controls="menu-report">
                                                <div class="nav-link-icon"><i class="fa-solid fa-chart-line fa-1_5x me-2"></i></div>
                                                Báo cáo
                                                <div class="sidenav-collapse-arrow">
                                                    <i class="fas fa-angle-down"></i>
                                                </div>
                                            </a>`;
                html += `
                            <div class="nav collapse" id="menu-report" data-bs-parent="#accordionSidenav">
                                 <nav class="sidenav-menu-nested nav accordion" id="accordionSidenavReport">`;

                if (EnumRoleList.find(e => e == 45)) { // READ_PURCHASE_AGREEMENT
                    html += `<a class="nav-link _permission_" data-enum="45" href="/ProductOrderStatistics/Index">Thống kê sản phẩm đặt hàng</a>`;
                }
                if (EnumRoleList.find(e => e == 41)) { // READ_PURCHASE_ORDER
                    html += `<a class="nav-link _permission_" data-enum="41" href="/ProductOrderStatistics/CustomerIndex">Thống kê khách hàng đặt hàng</a>`;
                }

                html += `</nav>
                                            </div>`;
            }

            // if (Userdata.roleIdList.find(e => e == ERoleType.Admin || e == ERoleType.Reporter || e == ERoleType.User)) {
            //             html += `<div id="div_user_QRcode" style="cursor: pointer;" class="nav-link  my-1" data-enum="14" href="">
            //                         <i class="fa fa-qrcode fa-1_5x me-2"></i> QR Telegram
            //                      </div>`;
            //         }

            $("#accordionSidenav").html(html)

            let pathname = window.location.pathname;
            let element_ = $("a[href='" + pathname + "']");
            let listUrl = ["/User/Index", "/Vendor/Index", "/Customer/Index", "/Product/Index", "/Unit/Index", "/Category/Index", "/TaxRateConfig/Index", "/SpecialProductTaxRate/Index", "/Material/Index", "/ProcessingType/Index"];
            let listReportUrl = ["/ProductOrderStatistics/Index", "/ProductOrderStatistics/CustomerIndex"];

            if (listUrl.includes(pathname)) {
                $("#category_").addClass("active");
                $("#menu-category").addClass("show");
            }
            else if (listReportUrl.includes(pathname)) {
                $("#report_").addClass("active");
                $("#menu-report").addClass("show");
            }
            // else
            {
                if (element_.length > 0) element_.addClass("active")
            }
        }
        function removeColumnNotEditAndDelete() {

            let pathname = window.location.pathname;
            switch (pathname) {
                // case "/User": {
                //     if (!checkPermissionGroupExist(["8", "9"], Userdata.enumActionList)) {
                //         $("#gridId").data("kendoGrid").hideColumn(8);
                //     }
                //     break;
                // }
                // case "/Tenants": {

                //     if (!checkPermissionGroupExist(["12", "13"], Userdata.enumActionList)) {
                //         $("#tenantListGrid").data("kendoGrid").hideColumn(4);
                //     }
                //     break;
                // }
                // case "/Urn": {

                //     if (!checkPermissionGroupExist(["4", "5"], Userdata.enumActionList) && !(Userdata.roleIdList.includes(ERoleType.User) && _isMobile)) {
                //         $("#gridId").data("kendoGrid").hideColumn(8);
                //     }
                //     break;
                // }
                // case "/StorageMap": {

                //     if (!checkPermissionGroupExist(["18", "19"], Userdata.enumActionList)) {
                //         $("#gridId").data("kendoGrid").hideColumn(5);
                //     }
                //     break;
                // }
                // case "/Reminder": {

                //     // if (!checkPermissionGroupExist(["1"], Userdata.enumActionList)) {
                //     //     $(gridId).data("kendoGrid").hideColumn(5);
                //     // }
                //     break;
                // }
                // case "/Configs": {

                //     if (!checkPermissionGroupExist(["15"], Userdata.enumActionList)) {
                //         $("#configListGrid").data("kendoGrid").hideColumn(6);
                //     }
                //     break;
                // }
                // default:
                //     return;
            }
        }


        $(document).ready(function () {

            ajax("GET", "/User/Authentication", null, (response) => {
                $("#pnAccount").text("Xin chào: " + response.data.name);

                // $("#titleHeader").text("An lạc tịnh đường " + response.data.tenantName);
                Userdata = response.data;
                menu(Userdata.enumActionList)
                // NotificationForUser.constructor();
                CheckPermission()
                removeColumnNotEditAndDelete()

                $("#div_user_QRcode").click(function (e) {
                    e.preventDefault();
                    ajax("GET", "/Home/QRCode", null, (res) => {
                        $("#QRcode_Popup .modal-body img").prop("src", res.data);
                        $("#QRcode_Popup").modal("show");
                    }, null, false);
                });

            },
                () => {
                    location.href = 'Account/Logout';
                }, false);
        });
    </script>
</body>

</html>
