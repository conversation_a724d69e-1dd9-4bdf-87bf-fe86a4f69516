﻿@{
    ViewData["Title"] = "Nhà cung cấp";
}

<div>

    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    @*   <div class="demo-section wide title">
    <h4 class="demo-section wide title">@ViewData["Title"]</h4>
    <nav id="breadcrumb"></nav>
    </div> *@
    <div id="divContent">
        <div id="gridId">
        </div>
    </div>
    <div id="window"></div>
    <div id="dialog"></div>
</div>
<script type="text/x-kendo-template" id="template_form_header">
    <div style="width:100%">
        <button id='create' class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-base _permission_' data-enum='6' style='margin-right: 5px;'><span class='k-icon k-i-plus k-button-icon'></span><span class='k-button-text'>Thêm</span></button>
    @*<button id='active' class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-base' style='margin-right: 5px;'><span class='k-icon k-i-check k-button-icon'></span><span class='k-button-text'>Active</span></button>
        <button id='importExcel' class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-base' style='margin-right: 5px;'><span class='k-icon k-i-file-excel k-button-icon'></span><span class='k-button-text'>Import Excel</span></button>*@
        <button id='exportExcel' class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-base' style='position: absolute; right: 16px;'><span class='k-icon k-i-file-excel k-button-icon'></span><span class='k-button-text'>Export Excel</span></button>
    </div>
</script>
<script type="text/javascript">
    let gridId = "#gridId";
    function addVendorProduct(vendorId) {
        let myWindow = $("#window");
        $("#window").html("<form id='formCreateAndEdit'></form>");
        let formData = {
            vendorId: vendorId,
            productId: 0,
            quantity: 0,
            price: 0,
            note: "",
        };

        let strSubmit = "Thêm";
        let title = "THÊM MỚI HÀNG HOÁ"

        $("#formCreateAndEdit").kendoForm({
            orientation: "vertical",
            formData: formData,
            type: "group",
            items: [
                {
                    field: "productId",
                    title: "Hàng hoá",
                    label: "Hàng hoá (*):",
                    editor: "dropdownlist",
                    dataSource: {
                        transport: {
                            read: {
                                url: "/Product/GetProductList",
                                datatype: "json",
                            },
                            parameterMap: function (data, type) {
                                if (type == "read") {
                                    return {
                                        pageSize: data.pageSize,
                                        pageNumber: data.page
                                    }
                                }
                            },
                        },
                        serverPaging: true,
                        serverFiltering: true,
                        pageSize: 20,
                        schema: {
                            type: 'json',
                            parse: function (response) {
                                if (response.isSuccess == false) {
                                    showErrorMessages(response.errorMessageList);
                                    return { data: [], total: 0 }
                                }
                                return response.data;
                            },
                            model: {
                                id: "id",
                                fields: {
                                    name: { type: "string" },
                                    code:{ type:"string" },
                                    price:{ type:"number" },
                                }
                            },
                            data:"data",
                            total:"total"
                        },
                    },
                    dataTextField:"name",
                    dataValueField:"id",
                    validation:{
                        required:true
                    }
                },
                {
                    field:"quantity",
                    title:"Số lượng",
                    label:"Số lượng (*):",
                    editor:"numerictextbox",
                    validation:{
                        required:true
                    }
                },
                {
                    field:"price",
                    title:"Giá bán",
                    label:"Giá bán (*):",
                    editor:"numerictextbox",
                    validation:{
                        required:true
                    }
                },
                {
                    field:"note",
                    title:"Ghi chú",
                    label:"Ghi chú:",
                },
            ],
            messages:{
                submit:strSubmit, clear:"Đặt lại"
            },
            submit:function(){

            }
        });


        setTimeout(() => {
            $("input[title='name']").focus();
        }, 500);

        function remove() {
            setTimeout(() => {
                if ($(".k-window #window").length > 0) {
                    $("#window").parent().remove();
                    $(gridId).after("<div id='window'></div>");
                }
            }, 200)
        }

        myWindow.kendoWindow({
            width: "500px",
            // height: "50vh",
            title: "",
            visible: false,
            actions: ["Close"],
            resizable: false,
            draggable: false,
            modal: true,
            close: function (e) {
                //$("#window").empty();
                remove();
            },
        }).data("kendoWindow").title(title).center();
        myWindow.data("kendoWindow").open();
    }

    function renderCreateOrEditForm(isCreate = true, dataVendor = {}) {
        let myWindow = $("#window");
        $("#window").html("<form id='formCreateAndEdit'></form>");

        let formData = {
            id: 0,
            name: "",
            address:"",
            // status: "",
            ...dataVendor
        };
        let strSubmit = "Thêm";
        let title = "THÊM MỚI"
        let element;
        if (isCreate == false) {
            strSubmit = "Sửa";
            title = "CẬP NHẬT";
        }
        $("#formCreateAndEdit").kendoForm({
            orientation: "vertical",
            formData: formData,
            type: "group",
            items: [

                {
                    field: "name",
                    title: "Họ tên",
                    label: "Họ tên (*):",
                    validation: {
                        validationMessage: "Vui lòng nhập họ tên",
                        required: true
                    },
                },
                {
                    field: "address",
                    title: "Địa chỉ",
                    label: "Địa chỉ:",
                    validation: {
                        address: true
                    },
                },

            ],
            messages: {
                submit: strSubmit, clear: "Đặt lại"
            },
            submit: function (e) {
                e.preventDefault();
                let dataItem = {
                    ...formData,
                    ...e.model,
                };

                if( dataItem.id > 0){
                    var response = ajax("PUT", "/Vendor/UpdateVendor/"+dataItem.id, dataItem, () => {
                        $(gridId).data("kendoGrid").dataSource.filter({});
                        myWindow.data("kendoWindow").close();
                    });
                }
                else{
                    var response = ajax("POST", "/Vendor/Create" , dataItem, () => {
                        $(gridId).data("kendoGrid").dataSource.filter({});
                        myWindow.data("kendoWindow").close();
                    });
                }
            },
            close: function (e) {
                $(this.element).empty();
            },
        });
        if(!isCreate){

        }

        // if (Userdata.roleIdList?.includes(ERoleType.Admin) == false) {
        //     $("#userName").data("kendoTextBox").enable(false);
        // }


        setTimeout(() => {
            $("input[title='name']").focus();
        }, 500);

        function remove() {
            setTimeout(() => {
                if ($(".k-window #window").length > 0) {
                    $("#window").parent().remove();
                    $(gridId).after("<div id='window'></div>");
                }
            }, 200)
        }

        myWindow.kendoWindow({
            width: "500px",
            // height: "50vh",
            title: "",
            visible: false,
            actions: ["Close"],
            resizable: false,
            draggable: false,
            modal: true,
            close: function (e) {
                //$("#window").empty();
                remove();
            },
        }).data("kendoWindow").title(title).center();
        myWindow.data("kendoWindow").open();
    }

    async function editVendor(id) {
        var response = ajax("GET", "/Vendor/GetVendorById/"+id, { vendorId: id }, (response) => {
            renderCreateOrEditForm(false, response.data);
        }, null, false);
    }
    function deleteVendor(id) {
        $('#dialog').kendoConfirm({
            title: "THÔNG BÁO XÓA NHÀ CUNG CẤP",
            content: "Bạn có chắc chắn xóa nhà cung cấp này không?",
            size: "medium",
            messages: {
                okText: "Đồng ý",
                cancel: "Hủy"

            },
        }).data("kendoConfirm").open().result.done(function () {
            var response = ajax("DELETE", "/Vendor/DeleteVendorById/"+id, {
                vendorId: id
            }, () => {
                $(gridId).data("kendoGrid").dataSource.filter({});
            });
        })

        $("#window").after("<div id='dialog'></div>");
    }


    async function ExportExcel() {
        let dataSheet1 = [
            {
                cells: [
                    {
                        value: "Mã nhà cung cấp", textAlign: "center", background: "#428dd8"
                    },
                    {
                        value: "Họ tên", textAlign: "center", background: "#428dd8"
                    },
                    {
                        value: "Địa chỉ", textAlign: "center", background: "#428dd8"
                    },
                ]
            }];

        var searchModel = getSearchModel();
        let postData = {
            ...searchModel,
            pageSize: 999999999,
            pageNumber: 1
        }
        let dataSourceVendor = null;
        var response = await ajax("GET", "/Vendor/GetVendorList", postData, (urnResponse) => {
            dataSourceVendor = urnResponse.data.data;
        }, null, false);
        if (dataSourceVendor == null) return;

        for (let index = 0; index < dataSourceVendor.length; index++) {
            dataSheet1.push({
                cells: [
                    { value: dataSourceVendor[index].code },
                    { value: dataSourceVendor[index].name },
                    { value: dataSourceVendor[index].address },
                ]
            })
        }


        var workbook = new kendo.ooxml.Workbook({
            sheets: [
                {
                    name: "Danh sách nhà cung cấp",
                    columns: [
                        { width: 200 }, { autoWidth: true }, { autoWidth: true },
                        { autoWidth: true }, { autoWidth: true }, { autoWidth: true }
                    ],
                    rows: dataSheet1,
                }
            ]
        });
        kendo.saveAs({
            dataURI: workbook.toDataURL(),
            fileName: "Danh sách nhà cung cấp _ " + kendo.toString(new Date(), "dd_MM_yyyy__HH_mm_ss") + ".xlsx"
        });
    }

    function getSearchModel() {
        let searchString = $("#searchString").val();

        return {
            searchString,
        };
    }
    function InitGrid() {
        let htmlToolbar = `
                <div id='toolbar' style=''  class='w-100 d-flex flex-column'>
                       <div class="row gx-0 row-gap-2 w-100">
                            <div class="col-xl-3 col-lg-4 col-md-4 col-sm-4 col-12">
                                <div class="pe-1">
                                    <label for="searchString">Tìm kiếm:</label>
                                    <input type="text" class=" w-100" id="searchString"/>
                                </div>
                            </div>
                            <div class="col-xl-3 col-lg-4 col-md-4 col-sm-4 col-12 d-flex align-items-sm-end ">
                                <div class="pe-1">
                                    <button id="search" title="Tìm kiếm" class = "k-button k-button-md k-rounded-md k-button-solid k-button-solid-primary  k-icon-button"><span class='k-icon k-i-search k-button-icon'></span><span class='k-button-text d-none'>Tìm kiếm</span></button>
                                    <button id='create' title="Thêm"  class='k-button k-button-md k-rounded-md k-button-solid k-button-solid-success _permission_' data-enum='16' ><span class='k-icon k-i-plus k-button-icon'></span><span class='k-button-text'>Thêm</span></button>
                                </div>
                            </div>
                        </div>

                            <div class="d-flex mt-2 flex-row w-100">
                            <button id="exportExcel" class="k-button k-button-md k-rounded-md k-button-outline k-button-outline-error"><span class="k-icon k-i-file-excel k-button-icon"></span><span class="k-button-ext">Export Excel</span></button>
                        </div>
                </div>
            `;

        $(gridId).kendoGrid({
            dataSource: {
                transport: {
                    read: {
                        url: "/Vendor/GetVendorList",
                        datatype: "json",
                    },
                    parameterMap: function (data, type) {
                        if (type == "read") {
                            var searchModel = getSearchModel();
                            return {
                                ...searchModel,
                                pageSize: data.pageSize,
                                pageNumber: data.page
                            }
                        }

                    },
                },
                serverPaging: true,
                serverFiltering: true,
                page: 1,
                pageSize: 20,
                schema: {
                    type: 'json',
                    parse: function (response) {
                        if (response.isSuccess == false) {
                            showErrorMessages(response.errorMessageList);
                            return {
                                data: [],
                                total: 0
                            }
                        }
                        return response.data;
                    },
                    model: {
                        id: "id",
                        fields: {
                            createdDate: { type: "date" },
                            updatedDate: { type: "date" },
                            stt: { type: "number" },

                        }
                    },
                    data: "data",
                    total: "total"
                },
            },
            selectable: true,
            pageable: {
                pageSizes: [10, 20, 50],
            },
            dataBinding: function (e) {
                record = (this.dataSource._page - 1) * this.dataSource._pageSize;
            },
            toolbar: htmlToolbar,
            // toolbar: "<div id='toolbar' style='width:100%'></div><div class='report-toolbar'>\</div>",
            columns: [
                {
                    field: "",
                    title: "STT",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: "#: ++record #",
                    width: 100
                },
                {
                    field: "code",
                    title: "Mã nhà cung cấp",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: 200,
                },
                {
                    field: "name",
                    title: "Họ tên",
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    width: 200,
                },
                {
                    field: "address",
                    title: "Địa chỉ",
                    width: 200,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "updatedDate",
                    title: "Ngày cập nhật",
                    width: 200,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                    template: '#: kendo.toString(kendo.parseDate(updatedDate), "dd/MM/yyyy HH:mm:ss")#',
                },
                {
                    field: "updatedByName",
                    title: "Người cập nhật",
                    width: 150,
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                    attributes: { style: "text-align:center;" },
                },
                {
                    field: "", title: "Thao tác", width: 200, attributes: { style: "text-align: center;" },
                    headerAttributes: { style: "text-align: center; justify-content: center" },
                        template: "<button style='margin-right:5px;' onclick=addVendorProduct(#=id#) title='Thêm hàng hoá' class='k-button k-button-md k-rounded-md k-button-solid-success _permission_1' data-enum='8'><span class='k-icon k-i-plus k-button-icon'></span></button>\
                        <button style='margin-right:5px;' onclick=editVendor(#=id#) title='Chỉnh sửa' class='k-button k-button-md k-rounded-md k-button-solid-warning _permission_' data-enum='8'><span class='k-icon k-i-track-changes k-button-icon'></span></button>\
                        <button style='margin-right:5px;' onclick=deleteVendor(#=id#) title='Xoá' class='k-button k-button-md k-rounded-md k-button-solid-error _permission_' data-enum='9'><span class='k-icon k-i-trash k-button-icon'></span></button>",
                }
            ],
            dataBound: function (e) {
                CheckPermission();
            }
        });


    }
    function InitKendoToolBar() {

        $("#search").kendoButton({
            icon: "search"
        });
        $("#search").click(async function (e) {
            var grid = $(gridId).data("kendoGrid");
            grid.dataSource.filter({});
        });
        $("#exportExcel").click(async function (e) {
            ExportExcel();
        });
        $("#searchString").kendoTextBox({
            icon: {
                type: "search",
                position: "end"  // Có thể là "start" hoặc "end"
            },
            placeholder: "Nhập từ khóa tìm kiếm..."
        });
        $("#create").kendoButton({
            icon: "plus"
        });

        $("#export").click(async function (e) {
            let grid = $(gridId).data("kendoGrid");
            grid.saveAsExcel();
        });


        $("#create").on('click', function () {
            renderCreateOrEditForm();
        });

    };

</script>
<script type="text/javascript">
    InitGrid();
    InitKendoToolBar();
    $(document).ready(function () {
        $(window).trigger("resize");

    });
</script>
<style>
    .k-form-buttons {
        justify-content: flex-end;
    }
</style>
